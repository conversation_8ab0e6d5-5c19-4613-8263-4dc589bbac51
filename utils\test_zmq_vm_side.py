#!/usr/bin/env python3
"""
ZMQ Connectivity Test - VM Side (Client/PUSH)
Tests connectivity from Windows VM to telemetry container.
Mimics the exact setup used by bulletproof_ipc_client.

Usage: python test_zmq_vm_side.py [mode]
  mode: 'push' (default) or 'req' for REQ/REP pattern
"""

import zmq
import time
import sys
import json
import socket as pysocket

def test_push_pull():
    """Test PUSH/PULL pattern (what bulletproof IPC uses)"""
    print("=" * 60)
    print("ZMQ PUSH/PULL Connectivity Test - VM Side")
    print("=" * 60)
    
    # Test configuration matching bulletproof_ipc_client
    targets = [
        ("tcp://telemetry-hub:5555", "bulk"),
        ("tcp://telemetry-hub:5556", "trading"),
        ("tcp://telemetry-hub:5557", "system"),
    ]
    
    # First, resolve hostname
    print("\n1. Resolving hostname 'telemetry-hub'...")
    try:
        ip = pysocket.gethostbyname('telemetry-hub')
        print(f"   ✓ Resolved to: {ip}")
    except Exception as e:
        print(f"   ✗ Failed to resolve: {e}")
        print("\nTry adding to C:\\Windows\\System32\\drivers\\etc\\hosts:")
        print("192.168.x.x  telemetry-hub")
        return False
    
    context = zmq.Context()
    results = []
    
    for endpoint, channel in targets:
        print(f"\n2. Testing {channel} channel on {endpoint}...")
        
        socket = context.socket(zmq.PUSH)
        # Match bulletproof_ipc_client settings
        socket.setsockopt(zmq.SNDHWM, 5000)
        socket.setsockopt(zmq.IMMEDIATE, 1)
        socket.setsockopt(zmq.LINGER, 1000)
        socket.setsockopt(zmq.TCP_KEEPALIVE, 1)
        socket.setsockopt(zmq.TCP_KEEPALIVE_IDLE, 30)
        
        try:
            print(f"   Connecting to {endpoint}...")
            socket.connect(endpoint)
            print("   ✓ Connected")
            
            # Send test messages
            for i in range(3):
                msg = {
                    "test": "ping",
                    "channel": channel,
                    "sequence": i,
                    "timestamp": time.time(),
                    "from": "vm_test"
                }
                
                # Test both JSON and multipart formats
                if i == 0:
                    # Simple JSON message
                    socket.send_json(msg)
                    print(f"   → Sent JSON ping #{i}")
                else:
                    # Multipart like bulletproof IPC
                    parts = [
                        f"testrade:{channel}".encode('utf-8'),
                        json.dumps(msg).encode('utf-8')
                    ]
                    socket.send_multipart(parts)
                    print(f"   → Sent multipart ping #{i}")
                
                time.sleep(0.1)
            
            results.append((channel, True))
            print(f"   ✓ {channel} channel test complete")
            
        except zmq.ZMQError as e:
            print(f"   ✗ ZMQ Error: {e}")
            results.append((channel, False))
        except Exception as e:
            print(f"   ✗ Error: {e}")
            results.append((channel, False))
        finally:
            socket.close()
    
    context.term()
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY:")
    for channel, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"  {channel:10s}: {status}")
    
    all_passed = all(r[1] for r in results)
    print("\nOverall: " + ("✓ ALL TESTS PASSED" if all_passed else "✗ SOME TESTS FAILED"))
    print("=" * 60)
    
    return all_passed

def test_req_rep():
    """Test REQ/REP pattern for bidirectional verification"""
    print("=" * 60)
    print("ZMQ REQ/REP Connectivity Test - VM Side")
    print("=" * 60)
    
    # Use port 5558 for REQ/REP test (not used by bulletproof IPC)
    endpoint = "tcp://telemetry-hub:5558"
    
    context = zmq.Context()
    socket = context.socket(zmq.REQ)
    socket.setsockopt(zmq.RCVTIMEO, 5000)  # 5 second timeout
    socket.setsockopt(zmq.LINGER, 0)
    
    try:
        print(f"Connecting to {endpoint}...")
        socket.connect(endpoint)
        print("✓ Connected")
        
        # Send ping and wait for pong
        for i in range(3):
            msg = {"type": "ping", "seq": i, "from": "vm"}
            print(f"\n→ Sending: {msg}")
            socket.send_json(msg)
            
            try:
                reply = socket.recv_json()
                print(f"← Received: {reply}")
                
                if reply.get("type") == "pong":
                    print(f"  ✓ Ping #{i} successful!")
                else:
                    print(f"  ? Unexpected reply: {reply}")
                    
            except zmq.Again:
                print(f"  ✗ Timeout waiting for reply to ping #{i}")
                return False
        
        print("\n✓ REQ/REP test successful!")
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False
    finally:
        socket.close()
        context.term()

def main():
    mode = sys.argv[1] if len(sys.argv) > 1 else "push"
    
    if mode == "push":
        success = test_push_pull()
    elif mode == "req":
        success = test_req_rep()
    else:
        print(f"Unknown mode: {mode}")
        print("Usage: python test_zmq_vm_side.py [push|req]")
        sys.exit(1)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()