"""
Process Manifests for TESTRADE

Declarative definitions of all managed subprocesses in the system.
These manifests define dependencies, health checks, and resource requirements.
"""

from typing import Any, Optional
import os
from interfaces.core.process_orchestration import (
    ProcessManifest, ProcessType, ProcessPriority, RestartPolicy,
    ResourceRequirements, HealthCheckConfig
)


def check_telemetry_process_health(process_info: Any) -> bool:
    """Health check for telemetry process."""
    # TODO: Implement actual health check (e.g., check if responding to health ping)
    return True


def check_ocr_process_health(process_info: Any) -> bool:
    """Health check for OCR process."""
    # TODO: Implement actual health check (e.g., check pipe responsiveness)
    return True


def check_ocr_daemon_health(process_info: Any) -> bool:
    """Health check for C++ OCR daemon EXE."""
    # TODO: Check if ocr_daemon.exe is responding via ZMQ health endpoint
    return True


def check_yolo_daemon_health(process_info: Any) -> bool:
    """Health check for C++ YOLO daemon EXE."""
    # TODO: Check if yolo.exe is responding via ZMQ health endpoint
    return True


def check_telemetry_daemon_health(process_info: Any) -> bool:
    """Health check for C++ telemetry daemon EXE."""
    # TODO: Check if telemetry.exe is processing frames via SHM
    return True


def run_ocr_daemon_process(config: Any) -> None:
    """Run the C++ OCR daemon EXE."""
    import subprocess
    import os

    # Path to the OCR daemon executable
    exe_path = os.path.join("modules", "ocr", "ocr_daemon", "bin", "ocr_daemon.exe")

    if not os.path.exists(exe_path):
        raise FileNotFoundError(f"OCR daemon not found at {exe_path}")

    # Run the EXE with proper working directory (bin holds DLLs)
    subprocess.run([exe_path], cwd=os.path.join("modules", "ocr", "ocr_daemon", "bin"), check=True)


def run_yolo_daemon_process(config: Any) -> None:
    """Run the C++ YOLO daemon EXE."""
    import subprocess
    import os

    # Path to the YOLO daemon executable
    exe_path = os.path.join("yolo_daemon", "bin", "yolo.exe")

    if not os.path.exists(exe_path):
        raise FileNotFoundError(f"YOLO daemon not found at {exe_path}")

    # Run the EXE with proper working directory
    subprocess.run([exe_path], cwd="yolo_daemon", check=True)


def run_telemetry_daemon_process(config: Any) -> None:
    """Run the C++ telemetry daemon EXE."""
    import subprocess
    import os

    # Path to the telemetry daemon executable
    exe_path = os.path.join("modules", "ocr", "telemetry", "bin", "telemetry.exe")

    if not os.path.exists(exe_path):
        raise FileNotFoundError(f"Telemetry daemon not found at {exe_path}")

    # Run the EXE with proper working directory (bin holds DLLs)
    subprocess.run([exe_path], cwd=os.path.join("modules", "ocr", "telemetry", "bin"), check=True)


def get_process_manifests(config: Any) -> list[ProcessManifest]:
    """
    Get all process manifests based on configuration.
    
    This function returns the complete set of process manifests,
    with some processes conditionally included based on config.
    
    Args:
        config: Application configuration object
        
    Returns:
        List of process manifests to be managed
    """
    manifests = []

    # Import mode detection
    from utils.testrade_modes import get_current_mode, TestradeMode
    current_mode = get_current_mode()

    # Check for headless mode flag
    is_headless = getattr(config, 'headless', False)
    
    # Determine if telemetry should be active
    enable_intellisense = getattr(config, 'enable_intellisense_logging', False)
    enable_ipc_dump = getattr(config, 'ENABLE_IPC_DATA_DUMP', False)
    should_enable_telemetry = (not is_headless) and enable_intellisense and enable_ipc_dump
    
    # DEBUG: Log telemetry decision
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"Process manifests - is_headless: {is_headless}, "
                f"enable_intellisense_logging: {enable_intellisense}, "
                f"ENABLE_IPC_DATA_DUMP: {enable_ipc_dump}, "
                f"should_enable_telemetry: {should_enable_telemetry}")
    
    # OCR Telemetry Process - DISABLED in ZMQ architecture
    # telemetry.exe handles telemetry processing via ZMQ
    if False:  # Permanently disabled - using external telemetry.exe
        # from modules.ocr.telemetry_process_main import run_telemetry_service_process
        from multiprocessing import Queue
        
        manifests.append(ProcessManifest(
            name="OCRTelemetryProcess",
            process_type=ProcessType.INFRASTRUCTURE,
            target_function=run_telemetry_service_process,
            args=(
                None,  # Queue will be injected by orchestrator
                getattr(config, 'LOG_BASE_DIR', 'logs'),
                config
            ),
            dependencies=[],  # No dependencies, starts first
            startup_timeout=5.0,
            shutdown_timeout=2.0,
            health_check=HealthCheckConfig(
                check_function=check_telemetry_process_health,
                check_interval_seconds=30.0,
                failure_threshold=3,
                startup_grace_period=5.0
            ),
            restart_policy=RestartPolicy.ALWAYS,
            restart_backoff_seconds=2.0,
            max_restart_attempts=5,
            resource_requirements=ResourceRequirements(
                priority=ProcessPriority.BELOW_NORMAL,
                cpu_affinity=None  # Let OS decide
            )
        ))
    
    # Main Telemetry Service Process (Infrastructure)
    if should_enable_telemetry:
        from core.services.telemetry_service_dual_mode import run_telemetry_service_dual_mode as run_telemetry_service
        from utils.global_config import CONFIG_FILE_PATH
        import zmq
        
        manifests.append(ProcessManifest(
            name="TelemetryService",
            process_type=ProcessType.INFRASTRUCTURE,
            target_function=run_telemetry_service,
            args=(CONFIG_FILE_PATH,),  # Single argument tuple
            dependencies=[],  # Independent infrastructure service
            startup_timeout=10.0,
            shutdown_timeout=5.0,
            health_check=None,  # TODO: Add ZMQ health check
            restart_policy=RestartPolicy.ALWAYS,
            restart_backoff_seconds=5.0,
            max_restart_attempts=3,
            resource_requirements=ResourceRequirements(
                priority=ProcessPriority.BELOW_NORMAL
            )
        ))
    
    # OCR Process (Application) - DISABLED in ZMQ architecture
    # External ocr_daemon.exe handles OCR processing via ZMQ
    if False:  # Permanently disabled - using external OCR daemon
        # from modules.ocr.ocr_process_main import run_ocr_service_process
        
        # Determine dependencies
        ocr_dependencies = []
        if should_enable_telemetry:
            ocr_dependencies.append("OCRTelemetryProcess")
        
        manifests.append(ProcessManifest(
            name="OCRProcess",
            process_type=ProcessType.APPLICATION,
            target_function=run_ocr_service_process,
            args=(
                None,  # Pipe connection will be injected
                None,  # Telemetry queue will be injected if needed
                None,  # OCR config will be built by orchestrator
                getattr(config, 'LOG_BASE_DIR', 'logs'),
                config
            ),
            dependencies=ocr_dependencies,
            startup_timeout=10.0,
            shutdown_timeout=5.0,
            health_check=HealthCheckConfig(
                check_function=check_ocr_process_health,
                check_interval_seconds=10.0,
                failure_threshold=3,
                startup_grace_period=10.0
            ),
            restart_policy=RestartPolicy.ON_FAILURE,
            restart_backoff_seconds=3.0,
            max_restart_attempts=3,
            resource_requirements=ResourceRequirements(
                priority=ProcessPriority.NORMAL,
                cpu_affinity=getattr(config, 'ocr_cpu_affinity', None)
            )
        ))
    
    # Future: Add more process manifests here
    # HotPath Listener - DISABLED in ZMQ architecture
    # ZMQ HotpathBridge in main.py handles hot path listening
    try:
        enable_hotpath_listener = getattr(config, 'enable_hotpath_listener', False)  # Default disabled
    except Exception:
        enable_hotpath_listener = False
    if False:  # Permanently disabled - using ZMQ HotpathBridge
        # from modules.ocr.hotpath_listener_process import run_hotpath_listener_process
        manifests.append(ProcessManifest(
            name="HotPathListenerProcess",
            process_type=ProcessType.INFRASTRUCTURE,
            target_function=run_hotpath_listener_process,
            args=(config,),
            dependencies=[],
            startup_timeout=5.0,
            shutdown_timeout=2.0,
            health_check=HealthCheckConfig(
                check_function=lambda _: True,
                check_interval_seconds=30.0,
                failure_threshold=3,
                startup_grace_period=5.0,
            ),
            restart_policy=RestartPolicy.ALWAYS,
            restart_backoff_seconds=2.0,
            max_restart_attempts=5,
            resource_requirements=ResourceRequirements(
                priority=ProcessPriority.BELOW_NORMAL
            )
        ))
    # ========= NEW EXE FLEET MANIFESTS =========

    # C++ OCR Daemon EXE - DISABLED to avoid duplicate launches; using 'OCRDaemon' manifest below

    # C++ Telemetry Daemon EXE - only in non-sealed modes
    if current_mode is not TestradeMode.TANK_SEALED:
        manifests.append(ProcessManifest(
            name="TelemetryDaemonEXE",
            process_type=ProcessType.INFRASTRUCTURE,
            target_function=run_telemetry_daemon_process,
            args=(config,),
            dependencies=["OCRDaemon"],  # Depends on OCR daemon for SHM/ZMQ data
            startup_timeout=10.0,
            shutdown_timeout=3.0,
            health_check=HealthCheckConfig(
                check_function=check_telemetry_daemon_health,
                check_interval_seconds=15.0,
                failure_threshold=3,
                startup_grace_period=5.0
            ),
            restart_policy=RestartPolicy.ON_FAILURE,  # Less critical than OCR
            restart_backoff_seconds=2.0,
            max_restart_attempts=3,
            resource_requirements=ResourceRequirements(
                priority=ProcessPriority.NORMAL,
                cpu_affinity=None
            )
        ))

    # C++ YOLO Daemon EXE - AI inference specialist (future)
    if getattr(config, 'enable_yolo_daemon', False):
        manifests.append(ProcessManifest(
            name="YOLODaemonEXE",
            process_type=ProcessType.APPLICATION,
            target_function=run_yolo_daemon_process,
            args=(config,),
            dependencies=["OCRDaemonEXE"],  # May share SHM resources
            startup_timeout=20.0,  # AI models take time to load
            shutdown_timeout=5.0,
            health_check=HealthCheckConfig(
                check_function=check_yolo_daemon_health,
                check_interval_seconds=20.0,
                failure_threshold=2,
                startup_grace_period=15.0
            ),
            restart_policy=RestartPolicy.ON_FAILURE,
            restart_backoff_seconds=5.0,
            max_restart_attempts=3,
            resource_requirements=ResourceRequirements(
                priority=ProcessPriority.HIGH,  # AI inference is performance-critical
                cpu_affinity=None
            )
        ))

    # - MarketDataProcess
    # - BacktestingProcess
    # - StrategyProcess
    # etc.

    # --- OPERATION ZMQ PURITY: EXE-BASED MANIFESTS ---

    # --- MANIFEST 1: The OCR Daemon (Always needed) ---
    manifests.append(ProcessManifest(
        name="OCRDaemon",
        process_type=ProcessType.INFRASTRUCTURE,
        executable_path="ocr_daemon.exe",
        working_directory="modules/ocr/ocr_daemon/bin",
        environment={
            "DXGI_ENABLE": "1",
            "MONITOR_INDEX": "0",
            "ROI_X1": "64",
            "ROI_Y1": "159",
            "ROI_X2": "681",
            "ROI_Y2": "296",
            "TESSDATA_PREFIX": "C:\\TANK\\modules\\ocr\\ocr_daemon\\bin\\tessdata",
            "CONTROL_JSON_PATH": "C:\\TANK\\utils\\control.json",
            "OCR_HOT_ZMQ_ADDR": "tcp://127.0.0.1:5556",
            "OCR_COLD_ZMQ_ADDR": "tcp://127.0.0.1:5558",
            "OCR_VERBOSE": "1"
        },
        health_check=HealthCheckConfig(
            check_function=check_ocr_daemon_health,
            check_interval_seconds=5.0,
            failure_threshold=3
        ),
        startup_timeout=10.0,
        shutdown_timeout=5.0
    ))

    # --- MANIFEST 2: The Telemetry Consumer (CONDITIONAL) ---
    if current_mode is not TestradeMode.TANK_SEALED:
        manifests.append(ProcessManifest(
            name="TelemetryConsumer",
            process_type=ProcessType.INFRASTRUCTURE,
            executable_path="telemetry.exe",
            dependencies=["OCRDaemon"],
            working_directory="modules/ocr/telemetry/bin",
            environment={
                "TELEM_IN_ADDR": "tcp://127.0.0.1:5558",
                "TELEM_ZMQ_ADDR": "tcp://telemetry-hub:5555"
            },
            health_check=HealthCheckConfig(
                check_function=check_telemetry_daemon_health,
                check_interval_seconds=10.0,
                failure_threshold=2
            ),
            startup_timeout=15.0,
            shutdown_timeout=5.0
        ))

    return manifests


# Process manifest registry for static analysis and tooling
PROCESS_REGISTRY = {
    "OCRTelemetryProcess": {
        "description": "Handles telemetry data from OCR process",
        "type": "infrastructure",
        "optional": True,
        "config_keys": ["enable_intellisense_logging", "ENABLE_IPC_DATA_DUMP"]
    },
    "TelemetryService": {
        "description": "Main telemetry service with ZMQ endpoints",
        "type": "infrastructure",
        "optional": True,
        "config_keys": ["enable_intellisense_logging"]
    },
    "OCRProcess": {
        "description": "Computer vision OCR processing",
        "type": "application",
        "optional": True,
        "config_keys": ["disable_ocr"]
    },
    "OCRDaemonEXE": {
        "description": "C++ OCR daemon with DXGI capture and preprocessing",
        "type": "application",
        "optional": False,
        "config_keys": ["disable_ocr"]
    },
    "TelemetryDaemonEXE": {
        "description": "C++ telemetry daemon for bulletproof IPC integration",
        "type": "infrastructure",
        "optional": True,
        "config_keys": ["enable_telemetry_daemon"]
    },
    "YOLODaemonEXE": {
        "description": "C++ YOLO daemon for AI inference",
        "type": "application",
        "optional": True,
        "config_keys": ["enable_yolo_daemon"]
    }
}
