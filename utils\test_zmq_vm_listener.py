#!/usr/bin/env python3
"""
ZMQ Test - VM Listener (Receives from Container)
Listens for messages sent from the telemetry container.

Usage: python test_zmq_vm_listener.py [mode]
  mode: 'pull' (default) or 'rep' for REQ/REP pattern
"""

import zmq
import json
import time
import sys
import threading

def listen_pull(port=7777):
    """Listen for PUSH messages from container"""
    print("=" * 60)
    print(f"ZMQ VM Listener (PULL on port {port})")
    print("=" * 60)
    
    context = zmq.Context()
    socket = context.socket(zmq.PULL)
    
    try:
        bind_addr = f"tcp://*:{port}"
        print(f"Binding to {bind_addr}...")
        socket.bind(bind_addr)
        print(f"✓ Listening on port {port}")
        print("\nWaiting for messages from container...")
        print("(Run test_zmq_to_vm.py on container to send messages)")
        print("Press Ctrl+C to stop.\n")
        
        msg_count = 0
        socket.setsockopt(zmq.RCVTIMEO, 1000)  # 1 second timeout for checking Ctrl+C
        
        while True:
            try:
                # Try to receive multipart first
                if socket.poll(1000):
                    try:
                        parts = socket.recv_multipart(flags=zmq.DONTWAIT)
                        msg_count += 1
                        
                        if len(parts) >= 2:
                            channel = parts[0].decode('utf-8')
                            payload = json.loads(parts[1].decode('utf-8'))
                            print(f"[{msg_count:3d}] ← Multipart: channel={channel}")
                            print(f"      payload: {payload}")
                        else:
                            # Single part message
                            data = json.loads(parts[0].decode('utf-8'))
                            print(f"[{msg_count:3d}] ← JSON: {data}")
                    except:
                        # Try as simple message
                        msg = socket.recv(flags=zmq.DONTWAIT)
                        msg_count += 1
                        try:
                            data = json.loads(msg.decode('utf-8'))
                            print(f"[{msg_count:3d}] ← JSON: {data}")
                        except:
                            print(f"[{msg_count:3d}] ← Raw: {msg}")
                            
            except zmq.Again:
                continue  # Timeout, continue
            except KeyboardInterrupt:
                print("\n\nStopping...")
                break
            except Exception as e:
                print(f"Error: {e}")
        
        print(f"\n✓ Stopped. Received {msg_count} messages total.")
        
    except Exception as e:
        print(f"✗ Failed to bind: {e}")
        print("\nTroubleshooting:")
        print("1. Check if port is already in use: netstat -an | findstr", port)
        print("2. Check Windows Firewall settings")
        print("3. Try running as Administrator")
    finally:
        socket.close()
        context.term()

def listen_rep(port=7778):
    """Listen for REQ messages and send REP"""
    print("=" * 60)
    print(f"ZMQ VM REP Server (port {port})")
    print("=" * 60)
    
    context = zmq.Context()
    socket = context.socket(zmq.REP)
    
    try:
        bind_addr = f"tcp://*:{port}"
        print(f"Binding REP socket to {bind_addr}...")
        socket.bind(bind_addr)
        print(f"✓ Listening on port {port}")
        print("\nWaiting for REQ messages from container...")
        print("Press Ctrl+C to stop.\n")
        
        msg_count = 0
        
        while True:
            try:
                # Wait for request
                request = socket.recv_json()
                msg_count += 1
                print(f"[{msg_count:3d}] ← Request: {request}")
                
                # Send reply
                if request.get("type") == "ping":
                    reply = {
                        "type": "pong",
                        "seq": request.get("seq", 0),
                        "from": "vm",
                        "echo": request
                    }
                    socket.send_json(reply)
                    print(f"      → Reply sent: {reply}")
                else:
                    reply = {"error": "Unknown request type"}
                    socket.send_json(reply)
                    
            except KeyboardInterrupt:
                print("\n\nStopping...")
                break
            except Exception as e:
                print(f"Error: {e}")
        
        print(f"\n✓ Stopped. Handled {msg_count} requests.")
        
    except Exception as e:
        print(f"✗ Failed to bind: {e}")
    finally:
        socket.close()
        context.term()

def check_firewall_windows():
    """Quick check for Windows firewall"""
    print("\nWindows Firewall Quick Check:")
    print("------------------------------")
    print("To allow incoming connections, run in elevated PowerShell:")
    print("")
    print("  # Allow ZMQ test ports")
    print("  New-NetFirewallRule -DisplayName 'ZMQ Test 7777' -Direction Inbound -LocalPort 7777 -Protocol TCP -Action Allow")
    print("  New-NetFirewallRule -DisplayName 'ZMQ Test 7778' -Direction Inbound -LocalPort 7778 -Protocol TCP -Action Allow")
    print("")
    print("To check existing rules:")
    print("  Get-NetFirewallRule | Where DisplayName -like '*ZMQ*'")
    print("")

def main():
    mode = sys.argv[1] if len(sys.argv) > 1 else "pull"
    
    print("Starting VM Listener...")
    check_firewall_windows()
    print("")
    
    if mode == "pull":
        listen_pull()
    elif mode == "rep":
        listen_rep()
    else:
        print(f"Unknown mode: {mode}")
        print("Usage: python test_zmq_vm_listener.py [pull|rep]")
        sys.exit(1)

if __name__ == "__main__":
    main()