{"SYSTEM_VERSION_TAG": "TESTRADE_v_INITIAL", "enable_time_sync": false, "rolling_window_seconds": 3, "fetch_interval_seconds": 5.0, "offset_storage_csv": "offsets_log.csv", "include_all_symbols": true, "disable_lightspeed_broker": false, "disable_price_fetching_service": true, "enable_full_performance_tracking_module": true, "ENABLE_FINE_GRAINED_PERFORMANCE_TRACKING": true, "PERF_METRICS_AUTO_CLEAR_INTERVAL_SEC": 3600.0, "enable_log_decorators": false, "enable_trace_decorators": false, "FEATURE_FLAG_USE_DIRECT_PRICE_RAW_TO_RISK_PMD": false, "FEATURE_FLAG_ENABLE_OBSERVABILITY_PUBLISHER": false, "USE_DI_CONTAINER": true, "ocr_conditioning_queue_max_size": 200, "ocr_conditioning_workers": 1, "initial_share_size": 1500, "add_type": "Equal", "reduce_percentage": 50.0, "manual_shares": 1000, "force_close_delay_sec": 10, "use_aggressive_chase_for_all_closes": true, "DEFAULT_OCR_TRADE_SIZE": 100, "ocr_signal_generator_workers": 4, "stable_cost_confirmation_frames": 3, "TELEMETRY_BATCH_SIZE": 500, "TELEMETRY_BATCH_TIMEOUT_SEC": 0.02, "TELEMETRY_MAX_PUBLISH_RATE_PER_SEC": 1000, "TELEMETRY_HEALTH_CHECK_INTERVAL_SEC": 1.0, "TELEMETRY_SERVICE_ENDPOINT": "tcp://127.0.0.1:7777", "TELEMETRY_BULK_ENDPOINT": "tcp://127.0.0.1:7777", "TELEMETRY_TRADING_ENDPOINT": "tcp://127.0.0.1:7778", "TELEMETRY_SYSTEM_ENDPOINT": "tcp://127.0.0.1:7779", "TELEMETRY_CLIENT_BUFFER_SIZE": 1000, "perf_metric_ttl_seconds": 604800, "perf_max_entries_per_metric": 10000, "enable_image_recording": false, "enable_raw_ocr_recording": true, "enable_intellisense_logging": false, "enable_network_diagnostics": false, "system_health_monitoring_interval_sec": 2.0, "system_health_baseline_interval": 60.0, "system_health_alert_cooldown_period_sec": 300.0, "system_health_metric_gather_workers": 4, "system_health_metric_gather_timeout_sec": 0.1, "system_health_cpu_threshold": 80.0, "system_health_app_core_cpu_threshold": 70.0, "system_health_ocr_process_cpu_threshold": 70.0, "system_health_memory_threshold": 85.0, "system_health_disk_threshold": 90.0, "system_health_ocr_process_max_cpu_percent": 90.0, "system_health_ocr_conditioning_max_q_size": 150, "system_health_event_bus_max_queue_size": 5000, "system_health_event_bus_max_avg_processing_ms": 100, "system_health_risk_max_latency_p95_ms": 200, "system_health_risk_max_pmd_queue_size": 100, "system_health_market_data_mmap_max_fill_pct": 90.0, "system_health_market_data_bulk_channel_clogged_duration_sec": 60, "enable_internal_market_data_streaming": true, "ENABLE_IPC_DATA_DUMP": true, "TESTRADE_MODE_COMMENT": "Mode detection: Set TESTRADE_MODE env var to TANK_SEALED, TANK_BUFFERED, or LIVE. If not set, uses legacy flags or defaults to LIVE.", "FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API": true, "development_mode": true, "log_level": "INFO", "enable_ocr_debug_logging": false, "enable_ocr_processing": true, "enable_trading_signals": true, "emergency_mode_active": false, "meltdown_use_safe_mode": true, "redis_stream_max_length": 500, "redis_stream_ttl_seconds": 3600, "redis_stream_raw_ocr": "testrade:raw-ocr-events", "redis_stream_cleaned_ocr": "testrade:cleaned-ocr-snapshots", "redis_stream_raw_ocr_data": "testrade:raw-ocr-data", "redis_stream_order_requests": "testrade:order-requests", "redis_stream_validated_orders": "testrade:validated-orders", "redis_stream_order_fills": "testrade:order-fills", "redis_stream_order_status": "testrade:order-status", "redis_stream_order_rejections": "testrade:order-rejections", "redis_stream_market_ticks": "testrade:filtered:market-trades", "redis_stream_market_quotes": "testrade:filtered:market-quotes", "redis_stream_market_trades": "testrade:market-data:trades", "redis_stream_filtered_market_quotes": "testrade:market-data:quotes", "redis_stream_risk_actions": "testrade:risk-actions", "redis_stream_position_updates": "testrade:position-updates", "redis_stream_enriched_position_updates": "testrade:enriched-position-updates", "redis_stream_image_grabs": "testrade:image-grabs", "redis_stream_broker_raw_messages": "testrade:broker-raw-messages", "redis_stream_broker_errors": "testrade:broker-errors", "redis_stream_trade_lifecycle_events": "testrade:trade-lifecycle-events", "redis_stream_trading_state_changes": "testrade:trading-state-changes", "redis_stream_config_changes": "testrade:config-changes", "redis_stream_maf_decisions": "testrade:maf-decisions", "redis_stream_gui_commands": "testrade:gui-commands", "redis_stream_command_responses": "testrade:gui-command-responses", "redis_stream_commands_from_gui": "testrade:commands:from_gui", "redis_stream_responses_to_gui": "testrade:responses:to_gui", "redis_stream_core_health": "testrade:health:core", "redis_stream_redis_instance_health": "testrade:health:redis_instance", "redis_stream_ipc_alerts": "testrade:alerts:ipc", "ENABLE_EMERGENCY_GUI": false, "emergency_status_port": 5561, "emergency_command_port": 5562, "emergency_http_port": 9998, "redis_stream_internal_raw_trades": "testrade:filtered:market-trades", "redis_stream_internal_raw_quotes": "testrade:filtered:market-quotes", "core_ipc_command_pull_address": "tcp://127.0.0.1:5560", "ocr_ipc_command_pull_address": "tcp://127.0.0.1:5559", "ocr_hot_zmq_addr": "tcp://127.0.0.1:5556", "ocr_cold_zmq_addr": "tcp://127.0.0.1:5558", "ocr_control_rep_addr": "tcp://0.0.0.0:5562", "telemetry_in_addr": "tcp://127.0.0.1:5558", "meltdown_short_window_sec": 30.0, "meltdown_long_window_sec": 90.0, "meltdown_price_drop_window": 1.5, "meltdown_momentum_window": 3.0, "meltdown_heavy_sell_multiplier": 2.5, "meltdown_price_drop_pct": 0.25, "meltdown_consecutive_bid_hits": 3, "meltdown_block_multiplier": 4.0, "meltdown_spread_widen_factor": 3.0, "meltdown_sell_ratio_threshold": 0.85, "meltdown_spread_threshold_pct": 0.2, "meltdown_halt_inactivity": 10.0, "meltdown_halt_min_trades": 5, "meltdown_exit_chase_attempts": 3, "meltdown_exit_chase_delay_ms": 300, "meltdown_exit_chase_aggression_cents": 0.02, "meltdown_chase_max_duration_sec": 30.0, "meltdown_chase_peg_update_interval_sec": 0.25, "meltdown_chase_peg_aggression_ticks": 1, "meltdown_chase_max_slippage_percent": 2.0, "meltdown_chase_use_initial_mkt": false, "meltdown_chase_initial_aggression_ticks": 1, "meltdown_chase_final_market_order": true, "CHASE_MAX_PENDING_LINK_TIME_SEC": 3.0, "CHASE_MAX_ACK_WAIT_TIME_SEC": 5.0, "risk_min_hold_seconds": 6.0, "risk_catastrophic_drop_threshold": 0.25, "risk_max_spread_pct_threshold": 0.2, "risk_score_critical_threshold": 0.75, "risk_score_high_threshold": 0.6, "risk_score_elevated_threshold": 0.4, "risk_max_shares_per_trade": 15000, "risk_max_notional_per_trade": 25000.0, "risk_max_position_size": 10000, "RISK_MAX_ORDER_QTY": 15000, "risk_pmd_queue_max_size": 500, "risk_pmd_workers": 1, "observability_log_directory": "data/observability_logs", "broker_connect_failure_threshold": 3, "broker_connect_recovery_timeout_sec": 60, "ALPACA_API_KEY": "PKR6DL0T0EQW14VH1GRM", "ALPACA_API_SECRET": "eAbyadtWAxVhJLxpkR50EEwrsUGY6Ue4Mnoz9WwV", "ALPACA_BASE_URL": "https://paper-api.alpaca.markets", "ALPACA_DATA_URL": "https://data.alpaca.markets", "ALPACA_USE_PAPER_TRADING": false, "ALPACA_SIP_URL": "wss://stream.data.alpaca.markets/v2/sip", "INITIAL_SYMBOLS_ALPACA": [], "price_cache_staleness_seconds_for_fallback": 30.0, "api_fallback_min_interval_per_symbol_sec": 0.5, "api_fallback_max_wait_ms": 150.0, "reliable_price_buy_offset_no_quote": 0.01, "reliable_price_sell_offset_no_quote": 0.01, "default_lmt_offset_dollars_ext_hours": 0.1, "default_lmt_offset_cents_ext_hours": 0.03, "feed_latency_price_match_tolerance_cents": 0.01, "suppressed_open_retry_window_seconds": 10.0, "suppressed_open_price_tolerance_cents": 0.05, "sync_grace_period_seconds": 3.0, "broker_get_positions_timeout_sec": 5.0, "cost_basis_initial_add_abs_delta_threshold": 0.02, "cost_basis_settling_duration_seconds": 5.0, "cost_basis_settling_override_abs_delta_threshold": 1.0, "rPnL_initial_reduce_increase_threshold": 50.0, "rPnL_settling_duration_seconds": 45.0, "rPnL_settling_override_increase_threshold": 100.0, "rPnL_settling_price_override_threshold_percent": 5.0, "rPnL_settling_price_override_threshold_abs": 0.5, "ocr_upscale_factor": 4, "ocr_force_black_text_on_white": true, "ocr_unsharp_strength": 1.7, "ocr_threshold_block_size": 25, "ocr_threshold_c": -6, "ocr_red_boost": 1, "ocr_green_boost": 1, "ocr_apply_text_mask_cleaning": true, "ocr_text_mask_min_contour_area": 5, "ocr_text_mask_min_width": 2, "ocr_text_mask_min_height": 1, "ocr_enhance_small_symbols": true, "ocr_symbol_max_height": 10, "ocr_period_comma_ratio_min": 0.2, "ocr_period_comma_ratio_max": 1.2, "ocr_period_comma_radius": 5, "ocr_hyphen_min_ratio": 3, "ocr_hyphen_min_height": 3, "OCR_INPUT_VIDEO_FILE_PATH": null, "ROI_COORDINATES": [64, 159, 681, 296], "VIDEO_LOOP_ENABLED": false, "ocr_capture_interval_seconds": 2.0, "TESSERACT_CMD": "C:\\Program Files\\Tesseract-OCR\\tesseract.exe", "flicker_filter": {"enable_flicker_filter": false, "known_add_shares": 10000, "time_sync_offset_sec": 0.0, "accept_tolerance_cents": 5.0, "recheck_count": 1, "recheck_delay_sec": 0.2, "price_check_mode": "synced_alpaca_price"}, "intellisense_mode": "live", "intellisense_config": null, "disable_broker": true, "TESTRADE_MODE": "LIVE", "TESTRADE_MODE_UPDATED": "2025-08-04T05:33:06.391189", "monitor_index": 1, "telemetry_enabled": true, "opencv_threads": 1, "opencv_optimized": true, "opencv_log_level": "fatal", "disable_ocr": true, "upscale_factor": 3, "unsharp_strength": 1.7, "threshold_block_size": 25, "threshold_c": -6, "red_boost": 1, "green_boost": 1, "blue_boost": 1, "force_black_on_white": true, "apply_text_mask_cleaning": true, "text_mask_min_area": 5, "text_mask_min_width": 2, "text_mask_min_height": 1, "enhance_small_symbols": true, "symbol_max_height": 10, "period_comma_radius": 5, "hyphen_min_height": 3, "hyphen_min_aspect_ratio": 3, "period_comma_aspect_min": 0.2, "period_comma_aspect_max": 1.2, "contrast_alpha": 1, "brightness_beta": 0, "clahe_clip_limit": 0}