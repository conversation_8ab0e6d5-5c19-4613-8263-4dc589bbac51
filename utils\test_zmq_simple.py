#!/usr/bin/env python3
"""
Simple ZMQ connectivity test - can be run from either side
"""

import zmq
import json
import time
import sys

def test_send():
    """Send test messages to telemetry container"""
    print("=" * 60)
    print("ZMQ Simple Send Test")
    print("=" * 60)
    
    context = zmq.Context()
    
    # Test each port
    ports = [
        (5555, "bulk"),
        (5556, "trading"),
        (5557, "system")
    ]
    
    for port, name in ports:
        print(f"\nTesting {name} on port {port}...")
        
        socket = context.socket(zmq.PUSH)
        socket.setsockopt(zmq.LINGER, 1000)
        
        # Try both localhost and telemetry-hub
        for host in ["localhost", "telemetry-hub", "127.0.0.1"]:
            endpoint = f"tcp://{host}:{port}"
            
            try:
                print(f"  Connecting to {endpoint}...")
                socket.connect(endpoint)
                
                # Send test message
                msg = {
                    "test": "ping",
                    "channel": name,
                    "host": host,
                    "timestamp": time.time()
                }
                
                parts = [
                    f"testrade:{name}".encode('utf-8'),
                    json.dumps(msg).encode('utf-8')
                ]
                
                socket.send_multipart(parts, flags=zmq.DONTWAIT)
                print(f"  ✓ Sent to {endpoint}")
                
                time.sleep(0.1)
                
            except Exception as e:
                print(f"  ✗ Failed {endpoint}: {e}")
        
        socket.close()
    
    context.term()
    print("\n" + "=" * 60)
    print("Test complete. Check container logs for received messages:")
    print("  docker logs telemetry-hub-container --tail 20")
    print("=" * 60)

if __name__ == "__main__":
    test_send()